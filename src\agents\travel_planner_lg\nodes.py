"""
LangGraph 节点函数实现 (V3.0 - 统一架构版)

每个节点函数负责执行特定的任务步骤，并更新状态。
支持两阶段工作流：意图分析 + ICP迭代规划
"""
import asyncio
import inspect
import json
import logging
from typing import Dict, Any
from datetime import datetime
from .state import StandardAgentState, TravelPlanState
from src.tools.unified_registry import unified_registry
from src.core.logger import get_logger

# 导入工具模块以触发工具注册
import src.tools.travel_planner.icp_tools

logger = get_logger("travel_planner_nodes")

def _fix_json_format(json_content: str) -> str:
    """
    修复常见的JSON格式问题，包括中文字符清理
    """
    import re

    # 开始修复过程
    fixed = json_content.strip()

    # 0. 【P0修复】清理中文字符，防止JSON解析失败
    # 移除数字后的中文单位
    fixed = re.sub(r'(\d+)\s*公里', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*小时', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*分钟', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*天', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*元', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*米', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*千米', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*公斤', r'\1', fixed)
    fixed = re.sub(r'(\d+)\s*人', r'\1', fixed)

    # 处理带引号的中文单位
    fixed = re.sub(r'"(\d+)\s*公里"', r'"\1"', fixed)
    fixed = re.sub(r'"(\d+)\s*小时"', r'"\1"', fixed)
    fixed = re.sub(r'"(\d+)\s*分钟"', r'"\1"', fixed)

    # 1. 修复单引号为双引号（但要小心不要破坏字符串内容）
    # 只替换属性名的单引号
    fixed = re.sub(r"'(\w+)':", r'"\1":', fixed)
    # 替换字符串值的单引号
    fixed = re.sub(r":\s*'([^']*)'", r': "\1"', fixed)

    # 2. 修复尾随逗号
    fixed = re.sub(r',\s*}', '}', fixed)
    fixed = re.sub(r',\s*]', ']', fixed)

    # 3. 修复缺少引号的属性名（但不要影响已经有引号的）
    fixed = re.sub(r'([{,]\s*)(\w+)(\s*):', r'\1"\2"\3:', fixed)

    # 4. 修复多余的逗号
    fixed = re.sub(r',\s*,', ',', fixed)

    # 5. 修复缺少逗号的情况
    fixed = re.sub(r'}\s*{', '},{', fixed)
    fixed = re.sub(r']\s*[', '],[', fixed)

    # 6. 修复布尔值和null值的大小写
    fixed = re.sub(r'\bTrue\b', 'true', fixed)
    fixed = re.sub(r'\bFalse\b', 'false', fixed)
    fixed = re.sub(r'\bNone\b', 'null', fixed)

    # 7. 移除注释（如果有的话）
    fixed = re.sub(r'//.*$', '', fixed, flags=re.MULTILINE)
    fixed = re.sub(r'/\*.*?\*/', '', fixed, flags=re.DOTALL)

    return fixed


def analyze_core_intent(state: TravelPlanState) -> Dict[str, Any]:
    """分析核心意图节点
    
    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析核心意图")
    
    # TODO: 调用分析服务，获得结构化数据
    # core_intent = analysis_service.analyze_core_intent(state['original_query'])
    
    # 临时实现 - 简单解析
    query = state.get('original_query', '')
    destinations = []
    
    # 简单的目的地提取逻辑
    if '上海' in query:
        destinations.append('上海')
    if '北京' in query:
        destinations.append('北京')
    if '杭州' in query:
        destinations.append('杭州')
    
    if not destinations:
        destinations = ['上海']  # 默认目的地
    
    core_intent = {
        'destinations': destinations,
        'duration_days': 3,  # 默认3天
        'travel_type': 'leisure',
        'budget_range': 'medium'
    }
    
    # 生成旁白文本
    narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"
    
    return {
        "destinations": destinations,
        "core_intent": core_intent,
        "current_narration_text": narration_text,
        "current_step": "核心意图分析完成",
        "progress_percentage": 20
    }


def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析多城市策略")
    
    destinations = state.get("destinations", [])
    
    if len(destinations) > 1:
        # 生成多城市策略
        total_days = state.get("core_intent", {}).get("duration_days", 3)
        days_per_city = max(1, total_days // len(destinations))
        
        strategy = {
            "order": destinations,
            "split": [
                {"city": city, "days": days_per_city} 
                for city in destinations
            ]
        }
        
        narration_text = f"建议您按照 {' -> '.join(destinations)} 的顺序游览，每个城市安排{days_per_city}天"
        
        # 根据模式决定是否需要澄清
        needs_clarification = state.get("execution_mode") == "interactive"
        
        return {
            "multi_city_strategy": strategy,
            "current_narration_text": narration_text,
            "clarification_needed": "multi_city_strategy" if needs_clarification else None,
            "current_step": "多城市策略分析完成",
            "progress_percentage": 30
        }
    
    # 单目的地，直接跳过
    return {
        "current_step": "单目的地，跳过多城市策略",
        "progress_percentage": 30
    }


def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析驾驶情境")
    
    # TODO: 调用用户画像服务获取车辆信息
    # vehicle_info = user_profile_service.get_vehicle_info(state['user_id'])
    
    # 临时实现
    vehicle_info = {
        "model": "Tesla Model Y",
        "nominal_range_km": 450,
        "charge_type": "fast"
    }
    
    # 设定驾驶策略
    if vehicle_info and vehicle_info.get("nominal_range_km"):
        driving_strategy = "range_aware"
        planning_range_km = vehicle_info["nominal_range_km"] * 0.8  # 保守系数
        narration_text = f"检测到您的{vehicle_info['model']}，我会按照续航的80%为您规划路线，确保行程安全"
    else:
        driving_strategy = "general_assistance"
        planning_range_km = None
        narration_text = "我会为您的自驾行程提供停车场和充电站信息"
    
    return {
        "user_vehicle_info": vehicle_info,
        "driving_strategy": driving_strategy,
        "planning_range_km": planning_range_km,
        "range_buffer_factor": 0.8,
        "current_narration_text": narration_text,
        "current_step": "驾驶情境分析完成",
        "progress_percentage": 40
    }


def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    整合分析景点、美食、住宿等偏好。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析用户偏好")
    
    # TODO: 调用分析服务
    # attraction_prefs = analysis_service.analyze_attraction_preferences(...)
    # food_prefs = analysis_service.analyze_food_preferences(...)
    # accommodation_prefs = analysis_service.analyze_accommodation_preferences(...)
    
    # 临时实现
    attraction_prefs = {
        "types": ["文化古迹", "自然风光"],
        "crowd_preference": "适中",
        "activity_level": "轻松"
    }
    
    food_prefs = {
        "cuisine_types": ["本地特色", "川菜"],
        "price_range": "中等",
        "dietary_restrictions": []
    }
    
    accommodation_prefs = {
        "type": "酒店",
        "star_rating": "4星",
        "location_preference": "市中心"
    }
    
    narration_text = "根据您的偏好，我会为您推荐文化古迹和自然风光，安排本地特色美食，选择市中心的4星酒店"
    
    return {
        "attraction_preferences": attraction_prefs,
        "food_preferences": food_prefs,
        "accommodation_preferences": accommodation_prefs,
        "current_narration_text": narration_text,
        "current_step": "用户偏好分析完成",
        "progress_percentage": 50
    }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束

    在分析的最后阶段决定是继续规划还是结束。
    """
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"

    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"


# ==================== V3.0 新增节点：两阶段意图分析 ====================

async def run_framework_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行核心框架分析 (V3.0)

    分析用户的旅行核心需求，包括目的地、天数、主题、多城市策略和自驾情境
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始核心框架分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "framework_analysis",
            "核心框架分析",
            "正在分析您的旅行核心需求和框架..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_framework_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_framework_analysis_prompt tool not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, current_time)

        # 调用LLM进行框架分析
        from src.agents.services.reasoning_service import ReasoningService
        reasoning_service = ReasoningService(llm_role="reasoning")

        # 构建消息格式
        system_prompt = """你是一个专业的旅行规划师，负责分析用户的旅行核心需求。
请根据用户查询分析出目的地、天数、主题等核心信息。

请严格按照以下JSON格式返回结果：
{
  "core_intent": {
    "destinations": ["目的地城市"],
    "travel_days": 天数(数字),
    "travel_theme": ["主题1", "主题2"],
    "budget_range": "预算等级",
    "group_size": 人数(数字),
    "group_members": ["成员类型"],
    "departure_city": "出发城市",
    "travel_time": "出行时间",
    "special_requirements": ["特殊需求"]
  },
  "confidence_score": 0.95,
  "analysis_notes": "分析说明"
}"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        # 调用LLM获取分析结果
        try:
            logger.info(f"[{task_id}] 开始调用真实LLM进行框架分析...")

            llm_response = await reasoning_service.simple_chat(
                messages=messages,
                max_tokens=2000
            )

            logger.info(f"[{task_id}] LLM响应长度: {len(llm_response)}")
            logger.info(f"[{task_id}] LLM原始响应内容: {repr(llm_response)}")

            # 尝试解析LLM响应为结构化数据
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_content = json_match.group()
                logger.info(f"[{task_id}] 提取的JSON内容: {repr(json_content)}")

                # 尝试使用更robust的JSON解析
                try:
                    framework_analysis = json.loads(json_content)
                    logger.info(f"[{task_id}] LLM框架分析成功，目的地: {framework_analysis.get('core_intent', {}).get('destinations', [])}")
                except json.JSONDecodeError as json_error:
                    logger.error(f"[{task_id}] JSON解析失败: {str(json_error)}")
                    logger.error(f"[{task_id}] 错误位置: line {json_error.lineno}, column {json_error.colno}")
                    logger.error(f"[{task_id}] 错误附近的内容: {json_content[max(0, json_error.pos-50):json_error.pos+50]}")

                    # 尝试修复常见的JSON格式问题
                    fixed_json = _fix_json_format(json_content)
                    if fixed_json != json_content:
                        logger.info(f"[{task_id}] 尝试修复后的JSON: {repr(fixed_json)}")
                        framework_analysis = json.loads(fixed_json)
                        logger.info(f"[{task_id}] JSON修复成功")
                    else:
                        raise json_error
            else:
                raise ValueError("无法从LLM响应中提取JSON")

        except Exception as llm_error:
            logger.warning(f"[{task_id}] LLM调用失败，使用模拟数据: {str(llm_error)}")

            # 【P0修复】从用户查询中提取地理位置和旅行天数
            import re
            travel_days = 3  # 默认值
            destinations = ["北京"]  # 默认值
            departure_city = "上海"  # 默认值

            if user_query:
                # 提取旅行天数
                day_patterns = [
                    r'(\d+)天',
                    r'(\d+)日',
                    r'(\d+)个?天',
                    r'(\d+)个?日'
                ]
                for pattern in day_patterns:
                    match = re.search(pattern, user_query)
                    if match:
                        travel_days = int(match.group(1))
                        logger.info(f"[{task_id}] 从用户查询中提取到旅行天数: {travel_days}")
                        break

                # 【P0修复】提取目的地城市
                # 常见城市列表
                cities = [
                    '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都', '重庆', '西安',
                    '天津', '青岛', '大连', '厦门', '武汉', '长沙', '沈阳', '哈尔滨', '济南', '昆明',
                    '贵阳', '兰州', '银川', '西宁', '拉萨', '乌鲁木齐', '呼和浩特', '石家庄', '太原',
                    '合肥', '福州', '南昌', '郑州', '长春', '海口', '三亚', '桂林', '丽江', '九寨沟',
                    '黄山', '泰山', '华山', '峨眉山', '庐山', '衡山', '嵩山', '恒山', '五台山',
                    '莆田', '泉州', '漳州', '龙岩', '三明', '南平', '宁德', '平潭'  # 添加福建城市
                ]

                # 查找目的地关键词
                destination_patterns = [
                    r'去(\w+)(?:玩|游|旅游|旅行)',
                    r'到(\w+)(?:玩|游|旅游|旅行)',
                    r'(\w+)(?:玩|游|旅游|旅行)',
                    r'(\w+)(?:\d+天|\d+日)',
                ]

                for pattern in destination_patterns:
                    matches = re.findall(pattern, user_query)
                    for match in matches:
                        if match in cities:
                            destinations = [match]
                            logger.info(f"[{task_id}] 从用户查询中提取到目的地: {match}")
                            break
                    if destinations != ["北京"]:  # 如果找到了目的地就跳出
                        break

                # 【P0修复】提取出发地
                departure_patterns = [
                    r'我在(\w+)',
                    r'从(\w+)出发',
                    r'(\w+)出发',
                ]

                for pattern in departure_patterns:
                    matches = re.findall(pattern, user_query)
                    for match in matches:
                        if match in cities or '大厦' in match or '机场' in match or '站' in match:
                            if match in cities:
                                departure_city = match
                            else:
                                # 如果是具体地点，尝试提取城市
                                for city in cities:
                                    if city in match:
                                        departure_city = city
                                        break
                            logger.info(f"[{task_id}] 从用户查询中提取到出发地: {departure_city}")
                            break
                    if departure_city != "上海":  # 如果找到了出发地就跳出
                        break

            # 如果LLM调用失败，使用基于用户查询提取的模拟数据作为后备
            framework_analysis = {
            "core_intent": {
                "destinations": destinations,  # 使用提取的目的地
                "travel_days": travel_days,  # 使用提取的天数
                "travel_theme": ["文化", "休闲"],
                "budget_range": "中等",
                "group_size": 2,
                "group_members": ["成人"],
                "departure_city": departure_city,  # 使用提取的出发地
                "travel_time": "近期",
                "special_requirements": []
            },
            "multi_city_strategy": {
                "is_multi_city": False,
                "city_priority": ["北京"],
                "transportation_between_cities": "高铁",
                "time_allocation": {"北京": 3},
                "route_optimization": "单城市深度游"
            },
            "driving_context": {
                "has_driving_needs": False,
                "driving_scenarios": [],
                "vehicle_requirements": "",
                "parking_considerations": [],
                "driving_experience_level": "一般",
                "fuel_type_preference": "汽油"
            },
            "analysis_confidence": {
                "core_intent_confidence": 0.9,
                "multi_city_confidence": 0.8,
                "driving_context_confidence": 0.7,
                "overall_confidence": 0.8
            },
            "clarification_needed": [],
            "assumptions_made": ["假设用户偏好文化类景点"]
        }

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "framework_analysis",
                "success",
                framework_analysis
            )

        logger.info(f"[{task_id}] 核心框架分析完成")

        return {
            "framework_analysis": framework_analysis,
            "current_phase": "framework_analysis_completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 核心框架分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "framework_analysis")

        return {
            "has_error": True,
            "error_message": f"核心框架分析失败: {str(e)}",
            "current_phase": "error"
        }


async def run_preference_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行个性化偏好分析 (V3.0)

    基于核心框架分析结果，深入分析用户的景点、美食、住宿偏好
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始个性化偏好分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "preference_analysis",
            "个性化偏好分析",
            "正在深入分析您的景点、美食、住宿偏好..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_preference_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_preference_analysis_prompt tool not found")

        # 获取前一步的结果
        framework_result = state.get("framework_analysis", {})
        if not framework_result:
            raise ValueError("framework_analysis result not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, framework_result, current_time)

        # 调用LLM进行偏好分析
        from src.agents.services.reasoning_service import ReasoningService
        reasoning_service = ReasoningService(llm_role="basic")

        # 构建消息格式
        system_prompt = """你是一个专业的旅行规划师，负责分析用户的个性化偏好。
请根据用户信息和框架分析结果，分析用户的景点、美食、住宿偏好。

请严格按照以下JSON格式返回结果：
{
  "attraction_preferences": {
    "preferred_types": ["类型1", "类型2"],
    "must_visit": ["必游景点"],
    "avoid_types": ["避免类型"],
    "accessibility_needs": ["无障碍需求"],
    "time_preferences": {
      "morning": "上午偏好",
      "afternoon": "下午偏好",
      "evening": "晚上偏好"
    },
    "activity_intensity": "活动强度",
    "crowd_tolerance": "人群容忍度",
    "photo_priority": "拍照重要性",
    "cultural_interest": "文化兴趣度"
  },
  "dining_preferences": {
    "cuisine_types": ["菜系1", "菜系2"],
    "dietary_restrictions": ["饮食限制"],
    "price_range": "价格范围",
    "atmosphere_pref": "氛围偏好",
    "local_food_interest": "本地美食兴趣"
  },
  "accommodation_preferences": {
    "hotel_types": ["酒店类型"],
    "location_priority": "位置优先级",
    "amenities_required": ["必需设施"],
    "budget_level": "预算等级"
  },
  "confidence_score": 0.90
}"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        # 调用LLM获取偏好分析结果
        try:
            logger.info(f"[{task_id}] 开始调用真实LLM进行偏好分析...")

            llm_response = await reasoning_service.simple_chat(
                messages=messages,
                max_tokens=2000
            )

            logger.info(f"[{task_id}] LLM偏好分析响应长度: {len(llm_response)}")
            logger.info(f"[{task_id}] LLM偏好分析原始响应: {repr(llm_response)}")

            # 尝试解析LLM响应为结构化数据
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_content = json_match.group()
                logger.info(f"[{task_id}] 提取的偏好分析JSON: {repr(json_content)}")

                # 尝试使用更robust的JSON解析
                try:
                    preference_analysis = json.loads(json_content)
                    logger.info(f"[{task_id}] LLM偏好分析成功")
                except json.JSONDecodeError as json_error:
                    logger.error(f"[{task_id}] 偏好分析JSON解析失败: {str(json_error)}")
                    logger.error(f"[{task_id}] 错误位置: line {json_error.lineno}, column {json_error.colno}")
                    logger.error(f"[{task_id}] 错误附近的内容: {json_content[max(0, json_error.pos-50):json_error.pos+50]}")

                    # 尝试修复常见的JSON格式问题
                    fixed_json = _fix_json_format(json_content)
                    if fixed_json != json_content:
                        logger.info(f"[{task_id}] 尝试修复后的偏好分析JSON: {repr(fixed_json)}")
                        preference_analysis = json.loads(fixed_json)
                        logger.info(f"[{task_id}] 偏好分析JSON修复成功")
                    else:
                        raise json_error
            else:
                raise ValueError("无法从LLM响应中提取JSON")

        except Exception as llm_error:
            logger.warning(f"[{task_id}] LLM调用失败，使用模拟数据: {str(llm_error)}")
            # 如果LLM调用失败，使用模拟数据作为后备
            preference_analysis = {
            "attraction_preferences": {
                "preferred_types": ["历史文化", "现代建筑"],
                "must_visit": ["故宫", "天安门"],
                "avoid_types": ["极限运动"],
                "accessibility_needs": ["无障碍通道"],
                "time_preferences": {
                    "morning": "户外景点",
                    "afternoon": "室内景点",
                    "evening": "夜景观赏"
                },
                "activity_intensity": "适中",
                "crowd_tolerance": "无所谓",
                "photo_priority": "很重要",
                "cultural_interest": "适度了解"
            },
            "food_preferences": {
                "cuisine_types": ["北京菜", "川菜"],
                "dietary_restrictions": ["不吃辣"],
                "spice_tolerance": "微辣",
                "budget_per_meal": {
                    "breakfast": "30-50元",
                    "lunch": "80-120元",
                    "dinner": "150-250元"
                },
                "dining_scenarios": ["家庭聚餐", "特色小吃"],
                "meal_timing": {
                    "breakfast": "酒店内用餐",
                    "lunch": "景点附近",
                    "dinner": "市区特色餐厅"
                },
                "local_specialties": "特别期待",
                "dining_atmosphere": "无所谓"
            },
            "accommodation_preferences": {
                "hotel_level": "四星级",
                "location_priority": ["交通便利", "景点附近"],
                "room_requirements": ["双床房", "无烟房"],
                "amenities_needed": ["WiFi", "早餐", "停车场"],
                "amenities_preferred": ["健身房", "游泳池"],
                "budget_per_night": "400-600元",
                "booking_flexibility": "可调整",
                "check_in_preferences": {
                    "early_check_in": False,
                    "late_check_out": True
                },
                "special_services": [],
                "brand_preference": "连锁"
            },
            "preference_confidence": {
                "attraction_confidence": 0.85,
                "food_confidence": 0.8,
                "accommodation_confidence": 0.9,
                "overall_confidence": 0.85
            },
            "personalization_insights": [
                "用户偏好文化类景点，适合安排历史古迹游览",
                "对美食有一定要求，建议安排特色餐厅",
                "住宿要求较高，注重便利性和舒适度"
            ],
            "recommendation_strategy": {
                "primary_focus": "平衡",
                "decision_factors": ["文化价值", "便利性", "性价比"],
                "flexibility_areas": ["用餐时间", "景点顺序"]
            }
        }

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "preference_analysis",
                "success",
                preference_analysis
            )

        logger.info(f"[{task_id}] 个性化偏好分析完成")

        return {
            "preference_analysis": preference_analysis,
            "current_phase": "preference_analysis_completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 个性化偏好分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "preference_analysis")

        return {
            "has_error": True,
            "error_message": f"个性化偏好分析失败: {str(e)}",
            "current_phase": "error"
        }


async def prepare_planning_context(state: StandardAgentState) -> Dict[str, Any]:
    """
    准备ICP规划上下文 (V3.0)

    整合两步分析结果，为ICP迭代规划准备统一的上下文
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始准备规划上下文")

    try:
        # 获取Planner Tools
        consolidate_tool = unified_registry.get_planner_tool("create_consolidated_intent")
        prepare_icp_tool = unified_registry.get_planner_tool("prepare_icp_context")
        extract_insights_tool = unified_registry.get_planner_tool("extract_key_insights")

        if not all([consolidate_tool, prepare_icp_tool, extract_insights_tool]):
            raise ValueError("Required planner tools not found")

        # 获取两步分析结果
        framework_analysis = state.get("framework_analysis", {})
        preference_analysis = state.get("preference_analysis", {})

        logger.info(f"[{task_id}] Framework analysis available: {bool(framework_analysis)}")
        logger.info(f"[{task_id}] Preference analysis available: {bool(preference_analysis)}")

        if not framework_analysis:
            logger.error(f"[{task_id}] Framework analysis results missing from state")
            logger.error(f"[{task_id}] Available state keys: {list(state.keys())}")
            raise ValueError("Framework analysis results missing")

        if not preference_analysis:
            logger.error(f"[{task_id}] Preference analysis results missing from state")
            logger.error(f"[{task_id}] Available state keys: {list(state.keys())}")
            raise ValueError("Preference analysis results missing")

        # 整合意图
        logger.info(f"[{task_id}] 调用create_consolidated_intent工具")
        logger.info(f"[{task_id}] Framework analysis keys: {list(framework_analysis.keys()) if framework_analysis else 'None'}")
        logger.info(f"[{task_id}] Preference analysis keys: {list(preference_analysis.keys()) if preference_analysis else 'None'}")

        consolidated_intent = consolidate_tool(framework_analysis, preference_analysis)

        logger.info(f"[{task_id}] Consolidated intent result: {bool(consolidated_intent)}")
        logger.info(f"[{task_id}] Consolidated intent keys: {list(consolidated_intent.keys()) if consolidated_intent else 'None'}")

        # 提取关键洞察
        key_insights = extract_insights_tool(consolidated_intent)

        # 准备ICP上下文
        icp_context = prepare_icp_tool(consolidated_intent)

        # 发布规划上下文准备完成事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "context_preparation",
                "success",
                {
                    "consolidated_intent": consolidated_intent,
                    "key_insights": key_insights,
                    "icp_context": icp_context
                }
            )

        logger.info(f"[{task_id}] 规划上下文准备完成")

        return {
            "consolidated_intent": consolidated_intent,
            "key_insights": key_insights,
            "icp_context": icp_context,
            "current_phase": "planning_ready"
        }

    except Exception as e:
        logger.error(f"[{task_id}] 规划上下文准备失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "context_preparation")

        return {
            "has_error": True,
            "error_message": f"规划上下文准备失败: {str(e)}",
            "current_phase": "error"
        }


async def run_icp_planning(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行ICP迭代式上下文规划 (V3.0) - 每日时序规划器

    实现两级循环结构：
    - 外循环：遍历每一天
    - 内循环：基于时间规划当天的每一个活动
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始ICP每日时序规划")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "icp_planning",
            "ICP每日时序规划",
            "开始智能时序规划，为您制定详细的每日行程..."
        )

    try:
        # 获取ICP工具
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        action_tool = unified_registry.get_planner_tool("select_next_action")
        observe_tool = unified_registry.get_planner_tool("observe_action_result")
        update_tool = unified_registry.get_planner_tool("update_planning_state")
        check_tool = unified_registry.get_planner_tool("check_planning_completion")

        if not all([think_tool, action_tool, observe_tool, update_tool, check_tool]):
            raise ValueError("Required ICP tools not found")

        # 获取规划上下文
        icp_context = state.get("icp_context", {})
        consolidated_intent = state.get("consolidated_intent", {})
        if not icp_context or not consolidated_intent:
            raise ValueError("ICP context or consolidated intent not found")

        # 获取旅行天数
        total_days = consolidated_intent.get("travel_days", 3)
        destinations = consolidated_intent.get("destinations", ["未知目的地"])
        main_destination = destinations[0] if destinations else "未知目的地"

        logger.info(f"[{task_id}] 规划{total_days}天{main_destination}行程")

        # 步骤1: 住宿先行 - 确定基点酒店
        logger.info(f"[{task_id}] 执行住宿先行策略")
        base_hotel = await _plan_accommodation_first(state, task_id, event_bus)

        # 初始化主POI池
        logger.info(f"[{task_id}] 初始化主POI池")
        master_poi_pool = await _initialize_master_poi_pool(state, task_id)
        logger.info(f"[{task_id}] 主POI池初始化完成，共{len(master_poi_pool)}个POI")

        # 初始化规划状态
        current_planning_state = {
            "daily_plans": state.get("daily_plans", {}),
            "daily_time_tracker": state.get("daily_time_tracker", {}),
            "total_budget_tracker": state.get("total_budget_tracker", 0.0),
            "tool_results": state.get("tool_results", {}),
            "planning_log": state.get("planning_log", []),
            "consolidated_intent": consolidated_intent,
            "base_hotel": base_hotel,
            "master_poi_pool": master_poi_pool,
            "remaining_pois": master_poi_pool.copy(),
            "used_poi_ids": [],  # 使用list而不是set以支持JSON序列化
            # 新增：时空状态管理
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": base_hotel,
                "is_done": False
            }
        }

        # 外循环：遍历每一天
        for day in range(1, total_days + 1):
            logger.info(f"[{task_id}] 开始规划第{day}天行程")

            # 更新当前天数和重置时间
            current_planning_state["icp_planner_state"]["current_day"] = day
            current_planning_state["icp_planner_state"]["current_time"] = "09:00"
            current_planning_state["icp_planner_state"]["current_location"] = base_hotel

            # 内循环：基于时间规划当天的每一个活动
            daily_activity_count = 0
            max_daily_activities = 6  # 每天最多6个活动
            daily_end_time = "21:00"  # 每日活动结束时间

            while (current_planning_state["icp_planner_state"]["current_time"] < daily_end_time and
                   daily_activity_count < max_daily_activities):

                logger.info(f"[{task_id}] 第{day}天 {current_planning_state['icp_planner_state']['current_time']} - 规划下一个活动")

                # 步骤0: 动态位置感知 (P2 新增) - 计算当前位置到剩余POI的距离
                current_location = current_planning_state["icp_planner_state"]["current_location"]
                remaining_pois = current_planning_state.get("remaining_pois", [])
                
                if remaining_pois:
                    # 调用P1阶段的原子工具进行动态位置感知
                    calculate_nearby_tool = unified_registry.get_planner_tool("calculate_nearby_pois_sorted_by_distance")
                    if calculate_nearby_tool:
                        nearby_poi_options = calculate_nearby_tool(current_location, remaining_pois)
                        current_planning_state["nearby_poi_options"] = nearby_poi_options
                        logger.info(f"[{task_id}] 动态位置感知完成，最近的POI：{nearby_poi_options[0].get('name', 'Unknown') if nearby_poi_options else 'None'}")
                    else:
                        logger.warning(f"[{task_id}] calculate_nearby_pois_sorted_by_distance工具未找到")
                        current_planning_state["nearby_poi_options"] = remaining_pois
                else:
                    current_planning_state["nearby_poi_options"] = []
                    logger.info(f"[{task_id}] 剩余POI池为空，结束规划")

                # 步骤1: 思考 (Think) - 由LLM驱动
                llm_decision = await think_tool(
                    current_planning_state,
                    icp_context
                )

                # 发布思考日志
                if event_bus:
                    await event_bus.notify_planning_log(
                        task_id,
                        llm_decision.get("thought", "LLM正在思考..."),
                        daily_activity_count + 1
                    )
                
                # 步骤2: 决策与行动 (Decide & Act)
                action = llm_decision.get("action", {})
                tool_name = action.get("tool_name")
                tool_params = action.get("parameters", {})
                
                if not tool_name:
                    logger.warning(f"[{task_id}] LLM未提供有效行动，跳过此步")
                    continue

                action_execution_result = None
                try:
                    if tool_name == "end_day_planning":
                        logger.info(f"[{task_id}] LLM决定结束第{day}天规划")
                        break

                    tool_func = unified_registry._action_tools.get(tool_name)
                    if not tool_func:
                        logger.warning(f"[{task_id}] LLM请求了未知或未注册的行动工具: {tool_name}")
                        continue

                    final_params = tool_params.copy()
                    if "current_state" in inspect.signature(tool_func).parameters:
                        final_params["current_state"] = current_planning_state

                    logger.info(f"[{task_id}] 准备执行行动工具: {tool_name} with params {list(final_params.keys())}")
                    action_execution_result = await unified_registry.execute_action_tool(
                        name=tool_name,
                        task_id=task_id,
                        **final_params
                    )

                    if tool_name == "select_poi_from_pool" and action_execution_result:
                        action_execution_result = [action_execution_result]

                except Exception as e:
                    logger.error(f"[{task_id}] 执行LLM行动 '{tool_name}' 失败: {str(e)}", exc_info=True)
                    action_execution_result = None

                # 步骤3 & 4 & 5: 观察、更新状态和调度 (P2 重构)
                if action_execution_result is not None:
                    observation = observe_tool(action, action_execution_result, current_planning_state)
                    current_planning_state = update_tool(current_planning_state, action, action_execution_result, observation)

                    if observation.get("success", False):
                        # P2重构：使用原子化的schedule_activity工具替代手动状态更新
                        activity_duration = llm_decision.get("estimated_duration_minutes", 120)
                        
                        # 获取选中的POI（从action_execution_result或latest_plan中）
                        selected_poi = None
                        if tool_name == "select_poi_from_pool" and action_execution_result:
                            # action_execution_result可能是单个POI或POI列表
                            if isinstance(action_execution_result, list) and action_execution_result:
                                selected_poi = action_execution_result[0]
                            elif isinstance(action_execution_result, dict):
                                selected_poi = action_execution_result
                        
                        # 如果没有直接获取到POI，从最新的daily_plan获取
                        if not selected_poi:
                            latest_plan = current_planning_state["daily_plans"].get(day, [])
                            if latest_plan:
                                selected_poi = latest_plan[-1]

                        if selected_poi:
                            # 调用P1阶段的原子化调度工具
                            schedule_activity_tool = unified_registry.get_planner_tool("schedule_activity")
                            if schedule_activity_tool:
                                try:
                                    schedule_result = await schedule_activity_tool(
                                        poi=selected_poi,
                                        activity_duration_minutes=activity_duration,
                                        current_state=current_planning_state
                                    )
                                    
                                    if schedule_result.get("success", False):
                                        # 更新时空状态
                                        current_planning_state["icp_planner_state"]["current_time"] = schedule_result["new_current_time"]
                                        current_planning_state["icp_planner_state"]["current_location"] = schedule_result["new_current_location"]
                                        daily_activity_count += 1
                                        logger.info(f"[{task_id}] 第{day}天活动{daily_activity_count}完成，原子调度成功：{schedule_result['new_current_time']} at {schedule_result['new_current_location']['name']}")
                                    else:
                                        logger.error(f"[{task_id}] 原子调度失败: {schedule_result.get('error', 'Unknown error')}")
                                        # 回退到手动更新
                                        current_time_str = current_planning_state["icp_planner_state"]["current_time"]
                                        new_time_str = _advance_time(current_time_str, activity_duration)
                                        current_planning_state["icp_planner_state"]["current_time"] = new_time_str
                                        daily_activity_count += 1
                                        
                                except Exception as e:
                                    logger.error(f"[{task_id}] 调用schedule_activity工具失败: {str(e)}")
                                    # 回退到手动更新
                                    current_time_str = current_planning_state["icp_planner_state"]["current_time"]
                                    new_time_str = _advance_time(current_time_str, activity_duration)
                                    current_planning_state["icp_planner_state"]["current_time"] = new_time_str
                                    daily_activity_count += 1
                            else:
                                logger.warning(f"[{task_id}] schedule_activity工具未找到，回退到手动更新")
                                # 回退到手动更新
                                current_time_str = current_planning_state["icp_planner_state"]["current_time"]
                                new_time_str = _advance_time(current_time_str, activity_duration)
                                current_planning_state["icp_planner_state"]["current_time"] = new_time_str
                                daily_activity_count += 1
                                
                                # 手动更新位置
                                current_planning_state["icp_planner_state"]["current_location"] = {
                                    "name": selected_poi.get("name"),
                                    "lat": selected_poi.get("lat"),
                                    "lon": selected_poi.get("lon")
                                }
                        else:
                            logger.warning(f"[{task_id}] 无法获取选中的POI信息，跳过原子调度")
                    else:
                        logger.warning(f"[{task_id}] 动作执行成功但观察失败，可能未添加新活动。")
                else:
                    logger.warning(f"[{task_id}] LLM行动 '{tool_name}' 未产生有效结果，无法更新状态。")


            # 当天规划完成
            logger.info(f"[{task_id}] 第{day}天规划完成，共安排{daily_activity_count}个活动")

        # 外循环结束 - 所有天数规划完成
        logger.info(f"[{task_id}] 所有{total_days}天规划完成")

        # 最终检查完成情况
        completion_check = check_tool(current_planning_state, icp_context)
        logger.info(f"[{task_id}] 每日时序规划完成，质量评分: {completion_check.get('quality_score', 0)}")

        # 生成最终结果
        final_itinerary = {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "planning_summary": {
                "total_days": total_days,
                "completion_rate": completion_check.get("completion_rate", 0),
                "quality_score": completion_check.get("quality_score", 0),
                "planning_log": current_planning_state.get("planning_log", [])
            },
            "metadata": {
                "planning_method": "ICP",
                "generated_at": datetime.now().isoformat(),
                "consolidated_intent": current_planning_state.get("consolidated_intent", {})
            }
        }

        # 确保发送所有行程更新事件（防止前端错过）
        if event_bus:
            daily_plans = current_planning_state.get("daily_plans", {})
            logger.info(f"[{task_id}] 最终发送所有行程更新事件: {len(daily_plans)}天")
            for day, activities in daily_plans.items():
                logger.info(f"[{task_id}] 发送第{day}天的{len(activities)}个活动")
                for activity in activities:
                    await event_bus.notify_itinerary_update(
                        task_id,
                        day,
                        activity
                    )
                    logger.info(f"[{task_id}] 最终发送行程更新: day={day}, activity={activity.get('name', 'Unknown')}")

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "icp_planning",
                "success",
                final_itinerary
            )

        logger.info(f"[{task_id}] ICP迭代规划完成")

        return {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "daily_time_tracker": current_planning_state.get("daily_time_tracker", {}),
            "total_budget_tracker": current_planning_state.get("total_budget_tracker", 0.0),
            "tool_results": current_planning_state.get("tool_results", {}),
            "planning_log": current_planning_state.get("planning_log", []),
            "final_itinerary": final_itinerary,
            "is_completed": True,
            "current_phase": "completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] ICP迭代规划失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "icp_planning")

        return {
            "has_error": True,
            "error_message": f"ICP迭代规划失败: {str(e)}",
            "current_phase": "error"
        }


async def _plan_accommodation_first(state: StandardAgentState, task_id: str, event_bus) -> Dict[str, Any]:
    """
    住宿先行策略 - 确定基点酒店

    Args:
        state: 当前状态
        task_id: 任务ID
        event_bus: 事件总线

    Returns:
        基点酒店信息
    """
    try:
        logger.info(f"[{task_id}] 执行住宿先行策略")

        consolidated_intent = state.get("consolidated_intent", {})
        destinations = consolidated_intent.get("destinations", ["未知目的地"])
        main_destination = destinations[0] if destinations else "未知目的地"

        # 搜索酒店
        hotel_results = await unified_registry.execute_action_tool(
            "search_poi",
            task_id=task_id,
            keywords="酒店",
            city=main_destination,
            types="100000",  # 酒店类型编码
            page_size=5
        )

        if hotel_results and len(hotel_results) > 0:
            # 选择第一个酒店作为基点
            base_hotel_poi = hotel_results[0]
            base_hotel = {
                "name": base_hotel_poi.get("name", f"{main_destination}酒店"),
                "lat": float(base_hotel_poi.get("location", "0,0").split(",")[1]) if "," in base_hotel_poi.get("location", "") else 0,
                "lon": float(base_hotel_poi.get("location", "0,0").split(",")[0]) if "," in base_hotel_poi.get("location", "") else 0,
                "address": base_hotel_poi.get("address", ""),
                "poi_id": base_hotel_poi.get("id", "base_hotel_001")
            }
            logger.info(f"[{task_id}] 选定基点酒店: {base_hotel['name']}")
        else:
            # 如果没有找到酒店，使用默认位置
            base_hotel = {
                "name": f"{main_destination}市中心",
                "lat": 39.9042,  # 默认北京坐标
                "lon": 116.4074,
                "address": f"{main_destination}市中心",
                "poi_id": "default_base_001"
            }
            logger.warning(f"[{task_id}] 未找到酒店，使用默认基点: {base_hotel['name']}")

        return base_hotel

    except Exception as e:
        logger.error(f"[{task_id}] 住宿先行策略失败: {str(e)}")
        # 返回默认基点
        return {
            "name": "默认基点",
            "lat": 39.9042,
            "lon": 116.4074,
            "address": "默认位置",
            "poi_id": "default_base_fallback"
        }


def _advance_time(current_time: str, duration_minutes: int) -> str:
    """
    推进时间

    Args:
        current_time: 当前时间，格式为"HH:MM"
        duration_minutes: 持续时间（分钟）

    Returns:
        新的时间字符串
    """
    try:
        from datetime import datetime, timedelta

        # 解析当前时间
        time_obj = datetime.strptime(current_time, "%H:%M")

        # 添加持续时间
        new_time_obj = time_obj + timedelta(minutes=duration_minutes)

        # 确保不超过当天23:59
        if new_time_obj.hour >= 24:
            new_time_obj = new_time_obj.replace(hour=23, minute=59)

        return new_time_obj.strftime("%H:%M")

    except Exception as e:
        logger.error(f"时间推进失败: {str(e)}")
        return "21:00"  # 返回默认结束时间


def _should_plan_meal(current_time: str, daily_activities: list) -> bool:
    """
    检查是否需要安排用餐时间

    Args:
        current_time: 当前时间
        daily_activities: 当天已安排的活动

    Returns:
        是否需要安排用餐
    """
    try:
        from datetime import datetime

        time_obj = datetime.strptime(current_time, "%H:%M")
        hour = time_obj.hour
        current_minutes = hour * 60 + time_obj.minute

        # 检查是否已有餐厅活动 - 修复大小写问题，支持多种格式
        has_lunch = False
        has_dinner = False

        for activity in daily_activities:
            # 检查多种可能的餐厅标识格式
            poi_type = activity.get("poi_type", "").lower()
            activity_type = activity.get("activity_type", "").lower()
            poi_details = activity.get("poi_details", {})
            poi_details_type = poi_details.get("poi_type", "").lower() if poi_details else ""

            # 判断是否为餐厅活动
            is_restaurant = (
                poi_type == "restaurant" or
                activity_type == "restaurant" or
                poi_details_type == "restaurant"
            )

            if is_restaurant:
                # 根据活动时间判断是午餐还是晚餐
                start_time = activity.get("start_time")
                if start_time:
                    try:
                        start_obj = datetime.strptime(start_time, "%H:%M")
                        start_minutes = start_obj.hour * 60 + start_obj.minute

                        # 午餐时间范围：11:30-14:30 (690-870分钟)
                        if 690 <= start_minutes <= 870:
                            has_lunch = True
                        # 晚餐时间范围：17:00-20:00 (1020-1200分钟)
                        elif 1020 <= start_minutes <= 1200:
                            has_dinner = True
                    except:
                        # 如果时间解析失败，保守地认为已有用餐安排
                        has_lunch = True
                        has_dinner = True

        # 午餐时间 (12:00-14:00) 或晚餐时间 (17:00-19:00)
        is_lunch_time = 720 <= current_minutes <= 840  # 12:00-14:00
        is_dinner_time = 1020 <= current_minutes <= 1140  # 17:00-19:00

        # 需要安排用餐的条件：在用餐时间且尚未安排对应的用餐
        need_lunch = is_lunch_time and not has_lunch
        need_dinner = is_dinner_time and not has_dinner

        return need_lunch or need_dinner

    except Exception as e:
        logger.error(f"用餐时间检查失败: {str(e)}")
        return False


def _get_meal_status_for_day(daily_activities: list) -> dict:
    """
    获取当天的用餐状态，用于跨天规划的状态检查

    Args:
        daily_activities: 当天已安排的活动

    Returns:
        dict: 包含has_lunch和has_dinner的状态字典
    """
    try:
        from datetime import datetime

        has_lunch = False
        has_dinner = False
        meal_details = []

        for activity in daily_activities:
            # 检查多种可能的餐厅标识格式
            poi_type = activity.get("poi_type", "").lower()
            activity_type = activity.get("activity_type", "").lower()
            poi_details = activity.get("poi_details", {})
            poi_details_type = poi_details.get("poi_type", "").lower() if poi_details else ""

            # 判断是否为餐厅活动
            is_restaurant = (
                poi_type == "restaurant" or
                activity_type == "restaurant" or
                poi_details_type == "restaurant"
            )

            if is_restaurant:
                start_time = activity.get("start_time")
                poi_name = activity.get("poi_name") or poi_details.get("name", "未知餐厅")

                if start_time:
                    try:
                        start_obj = datetime.strptime(start_time, "%H:%M")
                        start_minutes = start_obj.hour * 60 + start_obj.minute

                        # 午餐时间范围：11:30-14:30 (690-870分钟)
                        if 690 <= start_minutes <= 870:
                            has_lunch = True
                            meal_details.append(f"午餐: {poi_name} ({start_time})")
                        # 晚餐时间范围：17:00-20:00 (1020-1200分钟)
                        elif 1020 <= start_minutes <= 1200:
                            has_dinner = True
                            meal_details.append(f"晚餐: {poi_name} ({start_time})")
                    except:
                        # 时间解析失败时的处理
                        meal_details.append(f"用餐: {poi_name} (时间未知)")

        return {
            "has_lunch": has_lunch,
            "has_dinner": has_dinner,
            "meal_details": meal_details,
            "total_meals": len(meal_details)
        }

    except Exception as e:
        logger.error(f"获取用餐状态失败: {str(e)}")
        return {
            "has_lunch": False,
            "has_dinner": False,
            "meal_details": [],
            "total_meals": 0
        }


async def _initialize_master_poi_pool(state: StandardAgentState, task_id: str) -> list:
    """
    初始化主POI池 - 根据重构要求实现动态POI管理

    Args:
        state: 当前状态
        task_id: 任务ID

    Returns:
        主POI池列表
    """
    logger.info(f"[{task_id}] 开始初始化主POI池")

    try:
        # 获取意图分析结果
        consolidated_intent = state.get("consolidated_intent", {})

        # 尝试多种方式获取目的地信息
        destination = None
        if consolidated_intent.get("destination"):
            destination = consolidated_intent["destination"]
        elif consolidated_intent.get("destinations") and len(consolidated_intent["destinations"]) > 0:
            destination = consolidated_intent["destinations"][0]
        elif consolidated_intent.get("core_intent", {}).get("destinations") and len(consolidated_intent["core_intent"]["destinations"]) > 0:
            destination = consolidated_intent["core_intent"]["destinations"][0]
        elif consolidated_intent.get("core_intent", {}).get("destination"):
            destination = consolidated_intent["core_intent"]["destination"]

        # 如果还是没有找到，尝试从framework_analysis中获取
        if not destination:
            framework_analysis = state.get("framework_analysis", {})
            if framework_analysis.get("core_intent", {}).get("destinations"):
                destinations = framework_analysis["core_intent"]["destinations"]
                if isinstance(destinations, list) and len(destinations) > 0:
                    destination = destinations[0]

        travel_days = consolidated_intent.get("travel_days", 3)
        if not travel_days:
            travel_days = consolidated_intent.get("core_intent", {}).get("travel_days", 3)

        if not destination:
            logger.warning(f"[{task_id}] 目的地信息缺失，consolidated_intent: {consolidated_intent}")
            return []

        logger.info(f"[{task_id}] 获取到目的地: {destination}, 旅行天数: {travel_days}")

        # 搜索不同类型的POI
        poi_categories = [
            {"keywords": "景点", "types": "110000", "page_size": travel_days * 4},  # 景点类型
            {"keywords": "餐厅", "types": "050000", "page_size": travel_days * 3},  # 餐饮类型
            {"keywords": "酒店", "types": "100000", "page_size": travel_days + 1}   # 住宿类型
        ]

        master_poi_pool = []

        for category in poi_categories:
            try:
                logger.info(f"[{task_id}] 搜索{category['keywords']}类POI")

                # 调用POI搜索工具
                search_results = await unified_registry.execute_action_tool(
                    "search_poi",
                    keywords=category["keywords"],
                    city=destination,
                    types=category["types"],
                    page_size=category["page_size"],
                    task_id=task_id
                )

                if search_results and isinstance(search_results, list):
                    # 为每个POI添加类别标识和标准化数据结构
                    for poi in search_results:
                        # 确保POI有真实的ID
                        if not poi.get("id"):
                            import hashlib
                            unique_str = f"{poi.get('name', '')}_{poi.get('address', '')}_{poi.get('location', '')}"
                            poi["id"] = hashlib.md5(unique_str.encode()).hexdigest()[:12]

                        # 添加类别标识
                        poi["category"] = category["keywords"]
                        poi["search_types"] = category["types"]

                        # 标准化POI类型
                        poi["poi_type"] = _determine_poi_type_for_init(poi.get("typecode", ""), poi.get("type", ""))

                    master_poi_pool.extend(search_results)
                    logger.info(f"[{task_id}] {category['keywords']}类POI搜索成功，获得{len(search_results)}个结果")
                else:
                    logger.warning(f"[{task_id}] {category['keywords']}类POI搜索失败或无结果")

            except Exception as e:
                logger.error(f"[{task_id}] {category['keywords']}类POI搜索异常: {str(e)}")
                continue

        logger.info(f"[{task_id}] 主POI池初始化完成，总计{len(master_poi_pool)}个POI")
        return master_poi_pool

    except Exception as e:
        logger.error(f"[{task_id}] 主POI池初始化失败: {str(e)}")
        return []


async def _enrich_poi_data(planning_state: dict, target_day: str, task_id: str, event_bus=None):
    """
    POI数据丰富化流程 - 根据重构要求实现完整的数据丰富化

    Args:
        planning_state: 当前规划状态
        target_day: 目标天数
        task_id: 任务ID
        event_bus: 事件总线
    """
    logger.info(f"[{task_id}] 开始POI数据丰富化流程 - 第{target_day}天")

    try:
        daily_plans = planning_state.get("daily_plans", {})
        activities = daily_plans.get(target_day, [])

        enriched_activities = []

        for activity in activities:
            poi_id = activity.get("poi_id")
            poi_name = activity.get("name", "未知POI")

            if not poi_id:
                logger.warning(f"[{task_id}] POI缺少ID，跳过丰富化: {poi_name}")
                enriched_activities.append(activity)
                continue

            logger.info(f"[{task_id}] 开始丰富化POI: {poi_name} (ID: {poi_id})")

            # 发布丰富化开始日志
            if event_bus:
                await event_bus.notify_planning_log(
                    task_id,
                    f"正在为「{poi_name}」获取详细信息和图片...",
                    0
                )

            # 步骤1: 获取POI详情
            try:
                poi_details = await unified_registry.execute_action_tool(
                    "get_poi_details",
                    task_id=task_id,
                    poi_id=poi_id
                )

                if poi_details:
                    # 更新POI信息
                    activity.update({
                        "rating": poi_details.get("rating"),
                        "phone_number": poi_details.get("phone_number", ""),
                        "address": poi_details.get("address", activity.get("address", "")),
                        "opening_hours": poi_details.get("opening_hours", ""),
                        "price_level": poi_details.get("price_level"),
                        "tags": poi_details.get("tags", [])
                    })
                    logger.info(f"[{task_id}] POI详情获取成功: {poi_name}")
                else:
                    logger.warning(f"[{task_id}] POI详情获取失败: {poi_name}")

            except Exception as e:
                logger.error(f"[{task_id}] POI详情获取异常: {poi_name} - {str(e)}")

            # 步骤2: 获取POI图片
            try:
                poi_images = await unified_registry.execute_action_tool(
                    "get_poi_images",
                    task_id=task_id,
                    poi_id=poi_id,
                    max_images=3
                )

                if poi_images and isinstance(poi_images, list):
                    activity["image_urls"] = poi_images
                    logger.info(f"[{task_id}] POI图片获取成功: {poi_name} - {len(poi_images)}张图片")
                else:
                    activity["image_urls"] = []
                    logger.warning(f"[{task_id}] POI图片获取失败: {poi_name}")

            except Exception as e:
                logger.error(f"[{task_id}] POI图片获取异常: {poi_name} - {str(e)}")
                activity["image_urls"] = []

            # 步骤3: 生成POI介绍和建议时间（如果还没有）
            if not activity.get("introduction") or activity.get("introduction") == f"{poi_name}是一个值得游览的地方":
                poi_type = activity.get("poi_type", "ATTRACTION")
                activity["introduction"] = _generate_poi_introduction(poi_name, poi_type, activity.get("tags", []))

            if not activity.get("suggested_time"):
                poi_type = activity.get("poi_type", "ATTRACTION")
                activity["suggested_time"] = _generate_suggested_time(poi_type)

            # 发布丰富化完成日志
            if event_bus:
                await event_bus.notify_planning_log(
                    task_id,
                    f"「{poi_name}」信息丰富化完成",
                    0
                )

            enriched_activities.append(activity)
            logger.info(f"[{task_id}] POI丰富化完成: {poi_name}")

        # 更新规划状态
        planning_state["daily_plans"][target_day] = enriched_activities
        logger.info(f"[{task_id}] 第{target_day}天POI数据丰富化流程完成")

    except Exception as e:
        logger.error(f"[{task_id}] POI数据丰富化流程失败: {str(e)}")


def _generate_poi_introduction(poi_name: str, poi_type: str, tags: list) -> str:
    """生成POI介绍"""
    if poi_type == "RESTAURANT":
        return f"{poi_name}是一家值得品尝的餐厅，提供地道美食体验。"
    elif poi_type == "HOTEL":
        return f"{poi_name}是一家舒适的住宿选择，为您的旅行提供便利。"
    else:  # ATTRACTION
        if tags and len(tags) > 0:
            tag_desc = "、".join(tags[:2])
            return f"{poi_name}是一个{tag_desc}的热门景点，值得游览。"
        else:
            return f"{poi_name}是一个值得游览的热门景点。"


def _generate_suggested_time(poi_type: str) -> str:
    """生成建议时间"""
    if poi_type == "RESTAURANT":
        return "用餐时间 1-2小时"
    elif poi_type == "HOTEL":
        return "入住时间"
    else:  # ATTRACTION
        return "游览时间 2-3小时"


def _determine_poi_type_for_init(typecode: str, type_name: str) -> str:
    """
    根据高德API的typecode和type字段确定POI类型（用于初始化）

    Args:
        typecode: 高德API返回的类型编码
        type_name: 高德API返回的类型名称

    Returns:
        标准化的POI类型: ATTRACTION, RESTAURANT, HOTEL
    """
    # 高德地图POI类型编码映射
    if typecode:
        # 餐饮服务类型编码以05开头
        if typecode.startswith("05"):
            return "RESTAURANT"
        # 住宿服务类型编码以10开头
        elif typecode.startswith("10"):
            return "HOTEL"
        # 风景名胜类型编码以11开头
        elif typecode.startswith("11"):
            return "ATTRACTION"

    # 如果typecode不明确，根据type_name判断
    if type_name:
        type_lower = type_name.lower()
        if any(keyword in type_lower for keyword in ["餐厅", "饭店", "美食", "小吃", "咖啡", "茶楼"]):
            return "RESTAURANT"
        elif any(keyword in type_lower for keyword in ["酒店", "宾馆", "旅馆", "客栈", "民宿"]):
            return "HOTEL"
        elif any(keyword in type_lower for keyword in ["景点", "公园", "博物馆", "寺庙", "古迹", "广场"]):
            return "ATTRACTION"

    # 默认返回景点类型
    return "ATTRACTION"
