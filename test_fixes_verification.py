#!/usr/bin/env python3
"""
旅行规划系统修复验证测试脚本

验证以下修复：
1. 餐厅重复安排问题修复
2. 前端显示问题修复  
3. 全自动规划流程修复
4. 正则表达式错误修复
"""

import asyncio
import json
import requests
import time
from datetime import datetime

async def test_api_endpoint():
    """测试API端点是否正常工作"""
    print("🔍 测试API端点...")
    
    try:
        # 测试基础API
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ 基础API端点正常")
        else:
            print(f"❌ 基础API端点异常: {response.status_code}")
            return False
            
        # 测试静态文件
        response = requests.get("http://localhost:8000/static/index.html", timeout=10)
        if response.status_code == 200:
            print("✅ 静态文件服务正常")
        else:
            print(f"❌ 静态文件服务异常: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {str(e)}")
        return False

async def test_travel_planning_api():
    """测试旅行规划API"""
    print("\n🔍 测试旅行规划API...")
    
    try:
        # 准备测试数据
        test_data = {
            "user_query": "我在福州闽东大厦，这周末要去莆田玩两天",
            "user_id": "1",
            "execution_mode": "interactive",
            "vehicle_info": {
                "brand": "丰田",
                "model": "凯美瑞",
                "fuel_type": "汽油",
                "range": 450
            }
        }
        
        print(f"📋 发送测试请求: {test_data['user_query']}")
        
        # 发送请求
        response = requests.post(
            "http://localhost:8000/api/v3/travel-planner/plan",
            json=test_data,
            timeout=30,
            stream=True
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
        print("✅ API请求成功，开始接收SSE流...")
        
        # 解析SSE流
        events_received = []
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])  # 移除 'data: ' 前缀
                    events_received.append(data)
                    event_type = data.get('event', 'unknown')
                    print(f"📨 收到事件: {event_type}")
                    
                    # 如果收到错误事件，停止测试
                    if event_type == 'error':
                        print(f"❌ 收到错误事件: {data}")
                        return False
                        
                    # 如果收到完成事件，停止接收
                    if event_type in ['complete', 'planning_complete']:
                        print("✅ 收到完成事件，测试成功")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    continue
                    
            # 限制测试时间，避免无限等待
            if len(events_received) > 20:
                print("⚠️ 收到事件过多，停止测试")
                break
                
        print(f"📊 总共收到 {len(events_received)} 个事件")
        
        # 检查关键事件
        event_types = [event.get('event', 'unknown') for event in events_received]
        
        if 'start' in event_types:
            print("✅ 收到开始事件")
        else:
            print("❌ 未收到开始事件")
            
        if 'phase_start' in event_types:
            print("✅ 收到阶段开始事件")
        else:
            print("❌ 未收到阶段开始事件")
            
        return True
        
    except Exception as e:
        print(f"❌ 旅行规划API测试失败: {str(e)}")
        return False

async def test_regex_fix():
    """测试正则表达式修复"""
    print("\n🔍 测试正则表达式修复...")
    
    try:
        from src.agents.travel_planner_lg.nodes import _fix_json_format
        
        # 测试包含中文字符的JSON
        test_json = '''
        {
            "driving_distance": 200公里,
            "itinerary_type": "环形自驾",
            "parking_arrangements": ["莆田市区停车场"]
        }
        '''
        
        print("📋 测试原始JSON（包含中文字符）")
        
        # 尝试修复
        fixed_json = _fix_json_format(test_json)
        print("✅ JSON修复函数执行成功")
        
        # 尝试解析修复后的JSON
        parsed = json.loads(fixed_json)
        print("✅ 修复后的JSON可以正常解析")
        print(f"📊 修复结果: {parsed}")
        
        return True
        
    except Exception as e:
        print(f"❌ 正则表达式修复测试失败: {str(e)}")
        return False

async def test_user_profile_service():
    """测试用户画像服务"""
    print("\n🔍 测试用户画像服务...")
    
    try:
        from src.services.user_profile_database_service import UserProfileDatabaseService
        
        # 创建服务实例
        service = UserProfileDatabaseService()
        
        # 测试获取用户画像
        print("📋 获取用户ID=1的画像数据...")
        profile = await service.get_user_comprehensive_profile(1)
        
        if profile:
            print("✅ 用户画像获取成功")
            print(f"📊 用户ID: {profile.get('user_id')}")
            print(f"📊 画像完整度: {profile.get('profile_completeness', 0):.2f}")
            
            # 检查关键字段
            if profile.get('user_summary'):
                print("✅ 用户摘要存在")
            else:
                print("⚠️ 用户摘要为空")
                
            if profile.get('travel_profile'):
                print("✅ 旅行画像存在")
            else:
                print("⚠️ 旅行画像为空")
                
            return True
        else:
            print("❌ 用户画像为空")
            return False
            
    except Exception as e:
        print(f"❌ 用户画像服务测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始旅行规划系统修复验证测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试API端点
    test_results['api_endpoint'] = await test_api_endpoint()
    
    # 2. 测试正则表达式修复
    test_results['regex_fix'] = await test_regex_fix()
    
    # 3. 测试用户画像服务
    test_results['user_profile'] = await test_user_profile_service()
    
    # 4. 测试旅行规划API（最后测试，因为可能耗时较长）
    test_results['travel_planning'] = await test_travel_planning_api()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
