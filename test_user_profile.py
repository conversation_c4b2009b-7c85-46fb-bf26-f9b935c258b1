#!/usr/bin/env python3
"""
测试用户画像获取功能
验证是否能够从数据库中获取真实的用户画像数据
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_user_profile():
    """测试用户画像获取"""
    print("🔍 开始测试用户画像获取...")
    
    try:
        from src.services.user_profile_database_service import get_user_profile_database_service
        
        # 获取用户画像服务
        user_profile_service = get_user_profile_database_service()
        print("✅ 用户画像服务初始化成功")
        
        # 测试获取用户画像 (user_id=1)
        user_id = 1
        print(f"📋 正在获取用户ID {user_id} 的画像...")
        
        user_profile = await user_profile_service.get_user_comprehensive_profile(user_id)
        
        if user_profile:
            print("✅ 成功获取用户画像:")
            print(f"   - 用户ID: {user_profile.get('user_id', 'N/A')}")
            print(f"   - 用户名: {user_profile.get('username', 'N/A')}")
            print(f"   - 车辆信息: {user_profile.get('vehicle_info', 'N/A')}")
            print(f"   - 景点偏好: {user_profile.get('attraction_preferences', 'N/A')}")
            print(f"   - 美食偏好: {user_profile.get('food_preferences', 'N/A')}")
            print(f"   - 住宿偏好: {user_profile.get('accommodation_preferences', 'N/A')}")
            
            # 检查关键字段
            required_fields = ['user_id', 'username']
            missing_fields = [field for field in required_fields if field not in user_profile]
            
            if missing_fields:
                print(f"⚠️  缺少必要字段: {missing_fields}")
            else:
                print("✅ 用户画像数据完整")
                
        else:
            print(f"❌ 未找到用户ID {user_id} 的画像数据")
            
    except Exception as e:
        print(f"❌ 用户画像获取失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")

    try:
        from src.database.mysql_client import fetch_one, fetch_all

        # 测试查询用户数据
        query = "SELECT user_id, username FROM dh_user_profile.user_summaries WHERE user_id = %s"
        user_data = await fetch_one(query, (1,))

        if user_data:
            print("✅ 找到用户ID=1的数据:")
            print(f"   - 数据库记录: {user_data}")
        else:
            print("❌ 未找到用户ID=1的数据")

        # 测试查询用户旅行偏好
        query = """
        SELECT user_id, travel_style, accommodation_pref, transportation_pref
        FROM dh_tripplanner.user_travel_profiles
        WHERE user_id = %s
        """
        travel_prefs = await fetch_one(query, (1,))

        if travel_prefs:
            print("✅ 找到用户ID=1的旅行偏好:")
            print(f"   - 旅行风格: {travel_prefs.get('travel_style', 'N/A')}")
            print(f"   - 住宿偏好: {travel_prefs.get('accommodation_pref', 'N/A')}")
        else:
            print("❌ 未找到用户ID=1的旅行偏好")

    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 开始用户画像测试")
    print("=" * 50)
    
    await test_database_connection()
    await test_user_profile()
    
    print("=" * 50)
    print("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
