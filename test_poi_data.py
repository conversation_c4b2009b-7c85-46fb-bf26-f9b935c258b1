#!/usr/bin/env python3
"""
测试POI数据完整性
验证POI搜索结果是否包含所有必要字段，特别是图像URL
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_poi_search():
    """测试POI搜索功能"""
    print("🔍 开始测试POI搜索功能...")

    try:
        # 初始化工具注册表（模拟应用启动过程）
        print("📋 初始化工具注册表...")

        # 导入Action Tools（这会触发@register_action_tool装饰器）
        from src.agents.services.amap_service import AmapService

        # 导入Planner Tools（这会触发@register_planner_tool装饰器）
        import src.tools.travel_planner.consolidated_tools
        import src.tools.travel_planner.icp_tools

        from src.tools.unified_registry import unified_registry

        # 检查工具注册情况
        available_tools = unified_registry.get_action_tool_names()
        print(f"✅ 可用的Action Tools: {available_tools}")

        if "search_poi" not in available_tools:
            print("❌ search_poi工具未注册，尝试直接调用AmapService")

            # 直接使用AmapService进行测试
            from src.core.config import get_settings
            settings = get_settings()
            amap_service = AmapService(settings.amap_api_key)

            poi_results = await amap_service.search_poi(
                keywords="景点",
                city="莆田",
                types="风景名胜",
                page=1,
                offset=5
            )
        else:
            # 使用统一工具注册表
            poi_results = await unified_registry.execute_action_tool(
                "search_poi",
                keywords="景点",
                city="莆田",
                types="风景名胜",
                page=1,
                offset=5
            )
        
        if poi_results and len(poi_results) > 0:
            print(f"✅ 成功搜索到 {len(poi_results)} 个POI")
            
            # 检查第一个POI的数据完整性
            first_poi = poi_results[0]
            print(f"\n📍 检查第一个POI: {first_poi.get('name', 'N/A')}")
            
            # 必需字段检查
            required_fields = ['name', 'type', 'address', 'location']
            optional_fields = ['rating', 'price', 'photos', 'phone', 'business_hours']
            
            print("\n🔍 必需字段检查:")
            for field in required_fields:
                value = first_poi.get(field, 'N/A')
                status = "✅" if field in first_poi else "❌"
                print(f"   {status} {field}: {value}")
            
            print("\n🔍 可选字段检查:")
            for field in optional_fields:
                value = first_poi.get(field, 'N/A')
                status = "✅" if field in first_poi and value not in [None, 'N/A', ''] else "⚠️"
                print(f"   {status} {field}: {value}")
            
            # 特别检查图像URL
            photos = first_poi.get('photos', [])
            if photos and len(photos) > 0:
                print(f"\n📸 图像信息:")
                for i, photo in enumerate(photos[:3]):  # 只显示前3张
                    print(f"   图像 {i+1}: {photo}")
            else:
                print("\n⚠️  没有找到图像信息")
                
            # 检查位置信息
            location = first_poi.get('location', '')
            if location:
                if isinstance(location, str):
                    # 位置是字符串格式 "lng,lat"
                    coords = location.split(',')
                    if len(coords) == 2:
                        lng, lat = coords[0], coords[1]
                        print(f"\n📍 位置信息: 纬度={lat}, 经度={lng}")
                    else:
                        print(f"\n📍 位置信息: {location}")
                elif isinstance(location, dict):
                    lat = location.get('lat', 'N/A')
                    lng = location.get('lng', 'N/A')
                    print(f"\n📍 位置信息: 纬度={lat}, 经度={lng}")
                else:
                    print(f"\n📍 位置信息: {location}")
            else:
                print("\n❌ 缺少位置信息")
                
        else:
            print("❌ 未搜索到任何POI数据")
            
    except Exception as e:
        print(f"❌ POI搜索测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_poi_images():
    """测试POI图像获取功能"""
    print("\n🔍 测试POI图像获取功能...")

    try:
        # 直接使用AmapService进行测试
        from src.agents.services.amap_service import AmapService

        # 创建POI搜索服务
        amap_service = AmapService()

        # 测试获取特定POI的图像
        poi_name = "南山广化寺"
        print(f"📋 获取 {poi_name} 的图像...")

        # 先搜索POI
        poi_results = await amap_service.search_poi(
            city="莆田",
            keywords=poi_name,
            page=1,
            offset=1
        )

        if poi_results and len(poi_results) > 0:
            poi_id = poi_results[0].get('id')
            # 获取POI详情
            poi_detail = await amap_service.get_poi_details(poi_id)
            images = poi_detail.get('photos', [])
        
        if images and len(images) > 0:
            print(f"✅ 成功获取 {len(images)} 张图像")
            for i, image_url in enumerate(images[:3]):  # 只显示前3张
                print(f"   图像 {i+1}: {image_url}")
        else:
            print("⚠️  未获取到图像")
            
    except Exception as e:
        print(f"❌ POI图像获取测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_poi_details():
    """测试POI详情获取功能"""
    print("\n🔍 测试POI详情获取功能...")

    try:
        # 直接使用AmapService进行测试
        from src.agents.services.amap_service import AmapService

        # 创建POI搜索服务
        amap_service = AmapService()

        # 测试获取特定POI的详情
        poi_name = "南山广化寺"
        print(f"📋 获取 {poi_name} 的详情...")

        # 先搜索POI
        poi_results = await amap_service.search_poi(
            city="莆田",
            keywords=poi_name,
            page=1,
            offset=1
        )

        if poi_results and len(poi_results) > 0:
            poi_id = poi_results[0].get('id')
            details = await amap_service.get_poi_details(poi_id)
        else:
            details = None
        
        if details:
            print(f"✅ 成功获取POI详情")
            print(f"   名称: {details.get('name', 'N/A')}")
            print(f"   地址: {details.get('address', 'N/A')}")
            print(f"   评分: {details.get('rating', 'N/A')}")
            print(f"   电话: {details.get('phone', 'N/A')}")
            print(f"   营业时间: {details.get('business_hours', 'N/A')}")
            
            # 检查图像
            photos = details.get('photos', [])
            if photos:
                print(f"   图像数量: {len(photos)}")
            else:
                print("   图像数量: 0")
        else:
            print("❌ 未获取到POI详情")
            
    except Exception as e:
        print(f"❌ POI详情获取测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 开始POI数据完整性测试")
    print("=" * 50)
    
    await test_poi_search()
    await test_poi_images()
    await test_poi_details()
    
    print("=" * 50)
    print("✅ POI数据测试完成")

if __name__ == "__main__":
    asyncio.run(main())
